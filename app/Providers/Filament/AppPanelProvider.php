<?php

namespace App\Providers\Filament;

use App\Features\Analytics;
use App\Features\BulkImport;
use App\Features\Agents;
use App\Filament\Pages\App\Analytics\AgentAnalytics;
use App\Filament\Pages\App\Analytics\CollectionAnalytics;
use App\Filament\Pages\App\Analytics\ContributorAnalytics;
use App\Filament\Pages\App\Analytics\OrganizationAnalytics;
use App\Filament\Pages\App\Analytics\SourceAnalytics;
use App\Filament\Pages\App\BulkImportContent;
use App\Filament\Pages\App\Dashboard;
use App\Filament\Pages\App\RegisterTeam;
use App\Filament\Resources\AgentResource;
use App\Filament\Resources\AnthologyCollectionResource;
use App\Filament\Resources\ArticleSourceResource;
use App\Filament\Resources\BookSourceResource;
use App\Filament\Resources\CategoryResource;
use App\Filament\Resources\ChannelCollectionResource;
use App\Filament\Resources\CollectionResource;
use App\Filament\Resources\ContributorResource;
use App\Filament\Resources\EpisodeSourceResource;
use App\Filament\Resources\ListCollectionResource;
use App\Filament\Resources\MediaSourceResource;
use App\Filament\Resources\OrganizationResource;
use App\Filament\Resources\PeriodicalCollectionResource;
use App\Filament\Resources\PodcastCollectionResource;
use App\Filament\Resources\RssCollectionResource;
use App\Filament\Resources\SeriesCollectionResource;
use App\Filament\Resources\SourceResource;
use App\Filament\Resources\TeamResource;
use App\Filament\Resources\UrlSourceResource;
use App\Filament\Resources\WebsiteCollectionResource;
use App\Filament\Resources\YoutubeSourceResource;
use App\Http\Middleware\ApplyTenantScopes;
use App\Models\Agent;
use App\Models\Collection;
use App\Models\Organization;
use App\Models\Source;
use App\Models\Team;
use App\Services\Gatekeeper;
use App\Services\SpikeBillingProvider;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use EightyNine\Approvals\ApprovalPlugin;
use Exception;
use Filament\Panel;
use Filament\Resources\Resource;
use Filament\SpatieLaravelTranslatablePlugin;
use Filament\Support\Facades\FilamentAsset;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\Blade;
use JibayMcs\FilamentTour\FilamentTourPlugin;
use Laravel\Pennant\Feature;
use Leandrocfe\FilamentApexCharts\FilamentApexChartsPlugin;
use RalphJSmit\Filament\RecordFinder\FilamentRecordFinder;
use RickDBCN\FilamentEmail\FilamentEmail;
use Stephenjude\FilamentDebugger\DebuggerPlugin;
use Tapp\FilamentAuthenticationLog\FilamentAuthenticationLogPlugin;

class AppPanelProvider extends PanelProvider
{
    public function boot(): void
    {
        FilamentAsset::registerCssVariables([
            'background-image' => secure_asset('img/background-md-dark.png'),
        ]);
        parent::boot();
        Feature::resolveScopeUsing(fn ($driver) => get_current_team());
        Resource::scopeToTenant(false);
        FilamentAsset::registerCssVariables([
            '--background-image' => secure_asset('img/background-md-dark.png'),
        ]);
    }

    /**
     * @throws Exception
     */
    public function panel(Panel $panel): Panel
    {
        $panel = $this->apgUi($panel);
        $panel = $this->apgMiddleware($panel);

        return $panel
            ->id('app')
            ->domain(app_domain(env('APP_SUBDOMAIN_APP', 'app')))
            ->path('')
            ->tenant(Team::class, ownershipRelationship: 'owner')
            ->tenantMenu(false)
            ->tenantMiddleware([
                ApplyTenantScopes::class,
            ], isPersistent: true)
            ->tenantRegistration(RegisterTeam::class)
            ->tenantBillingProvider(new SpikeBillingProvider())
            ->tenantBillingRouteSlug('billing')
//            ->requiresTenantSubscription()
            ->globalSearch(false)
            ->resources([
                'agents' => AgentResource::class,
                'categories' => CategoryResource::class,
                'collections' => CollectionResource::class,
                'contributors' => ContributorResource::class,
                'sources' => SourceResource::class,
                'podcast_episodes' => EpisodeSourceResource::class,
                'urls' => UrlSourceResource::class,
                'videos' => YoutubeSourceResource::class,
                'media' => MediaSourceResource::class,
                'articles' => ArticleSourceResource::class,
                'books' => BookSourceResource::class,
                'podcasts' => PodcastCollectionResource::class,
                'channels' => ChannelCollectionResource::class,
                'rss_feeds' => RssCollectionResource::class,
                'websites' => WebsiteCollectionResource::class,
                'lists' => ListCollectionResource::class,
                'anthologies' => AnthologyCollectionResource::class,
                'series' => SeriesCollectionResource::class,
                'periodicals' => PeriodicalCollectionResource::class,
                'teams' => TeamResource::class,
                'organizations' => OrganizationResource::class,
            ])
            ->brandLogo(secure_asset('img/apologist-ignite-light.svg'))
            ->darkModeBrandLogo(secure_asset('img/apologist-ignite-dark.svg'))
            ->discoverPages(in: app_path('Filament/Pages/App'), for: 'App\\Filament\\Pages\\App')
            ->pages([
                Dashboard::class,
                BulkImportContent::class,
                AgentAnalytics::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->spaUrlExceptions([
                '*/switch',
            ])
            ->renderHook(PanelsRenderHook::BODY_START, fn () => view('components.app.impersonate-banner'))
            ->renderHook(PanelsRenderHook::USER_MENU_BEFORE, fn () => view('components.app.team-menu'))
            ->renderHook(
                PanelsRenderHook::PAGE_END,
                fn (array $scopes) => ! is_mobile() ? Blade::render('@livewire(\'welcome-modal\')', ['scopes' => $scopes]) : '',
                scopes: [
                    Dashboard::class,
                    \App\Filament\Resources\TeamResource\Pages\EditRecord::class,
                ],
            )
            ->renderHook(
                PanelsRenderHook::PAGE_START,
                fn (array $scopes) => view('components.app.notices.trial', ['scopes' => $scopes])
            )
            ->renderHook(
                PanelsRenderHook::PAGE_HEADER_ACTIONS_BEFORE,
                fn (array $scopes) => view('components.app.approval-status', ['scopes' => $scopes]),
                scopes: $this->getListingScopes(),
            )
            ->renderHook(
                PanelsRenderHook::PAGE_HEADER_WIDGETS_AFTER,
                fn (array $scopes) => view('components.app.notices.rejection-comment', ['scopes' => $scopes]),
                scopes: $this->getListingScopes(),
            )
            ->renderHook(
                PanelsRenderHook::PAGE_HEADER_WIDGETS_AFTER,
                fn (array $scopes) => view('components.app.notices.merged-contributor', ['scopes' => $scopes]),
                scopes: $this->getContributorListingScopes(),
            )
            ->renderHook(
                PanelsRenderHook::PAGE_HEADER_WIDGETS_AFTER,
                fn (array $scopes) => view('components.app.notices.create-agent', ['scopes' => $scopes]),
                scopes: $this->getAgentCreateScopes(),
            )
            ->renderHook(
                PanelsRenderHook::PAGE_SUB_NAVIGATION_TOP_BEFORE,
                fn (array $scopes) => view('components.app.notices.edit-agent', ['scopes' => $scopes]),
                scopes: $this->getAgentEditScopes(),
            )
//            ->renderHook(
//                PanelsRenderHook::PAGE_HEADER_WIDGETS_AFTER,
//                fn (array $scopes) => view(
//                    'components.listing-stats',
//                    [
//                        'scopes' => $scopes,
//                        'dashboard' => true,
//                        'model' => Source::class,
//                        'title' => 'Source Listing Performance',
//                    ]
//                ),
//                scopes: [
//                    Dashboard::class,
//                ],
//            )
//            ->renderHook(
//                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
//                fn (array $scopes) => view(
//                    'components.listings-time-series',
//                    [
//                        'scopes' => $scopes,
//                    ]
//                ),
//                scopes: $this->getListingAnalyticsScopes(),
//            )
//            ->renderHook(
//                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
//                fn (array $scopes) => view(
//                    'components.listing-stats',
//                    [
//                        'scopes' => $scopes,
//                        'dashboard' => false,
//                        'model' => Collection::class,
//                        'title' => 'Collection Listing Stats',
//                    ]
//                ),
//                scopes: $this->getCollectionListingScopes(),
//            )
//            ->renderHook(
//                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
//                fn (array $scopes) => view(
//                    'components.listing-stats',
//                    [
//                        'scopes' => $scopes,
//                        'dashboard' => false,
//                        'model' => Organization::class,
//                        'title' => 'Organization Listing Stats',
//                    ]
//                ),
//                scopes: $this->getOrganizationListingScopes(),
//            )
//            ->renderHook(
//                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
//                fn (array $scopes) => view(
//                    'components.listing-stats',
//                    [
//                        'scopes' => $scopes,
//                        'dashboard' => false,
//                        'model' => Contributor::class,
//                        'title' => 'Contributor Listing Stats',
//                    ]
//                ),
//                scopes: $this->getContributorListingScopes(),
//            )
//            ->renderHook(
//                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
//                fn (array $scopes) => view(
//                    'components.agent-usage',
//                    [
//                        'scopes' => $scopes,
//                        'list' => in_array('App\Filament\Resources\AgentResource\Pages\ListRecords', $scopes),
//                        'title' => 'Agent Usage',
//                    ]
//                ),
//                scopes: $this->getAgentUsageScopes(),
//            )
//            ->renderHook(
//                PanelsRenderHook::PAGE_FOOTER_WIDGETS_BEFORE,
//                fn (array $scopes) => view(
//                    'components.listing-stats',
//                    [
//                        'scopes' => $scopes,
//                        'dashboard' => false,
//                        'model' => Agent::class,
//                        'title' => 'Agent Listing Stats',
//                    ]
//                ),
//                scopes: $this->getAgentListingScopes(),
//            )
            ->plugins($this->getPlugins())
        ;
    }

    protected function getPlugins(): array
    {
        return [
            $this->getEnvironmentIndicatorPlugin(),
            FilamentShieldPlugin::make(),
            FilamentApexChartsPlugin::make(),
            DebuggerPlugin::make(),
            FilamentAuthenticationLogPlugin::make(),
            new FilamentEmail,
            ApprovalPlugin::make(),
            FilamentTourPlugin::make(),
            SpatieLaravelTranslatablePlugin::make()
                ->defaultLocales(array_keys(config('agent.languages'))),
            FilamentRecordFinder::make(),
        ];
    }

    /**
     * @return array[]
     */
    protected function getNav(): array
    {

        $organizationUrl = OrganizationResource::getUrl('create');
        $organizationId = null;
        if (get_current_team()->hasOrganization()) {
            $organizationId = get_current_team()->organization->id;
            $organizationUrl = OrganizationResource::getUrl('edit', ['record' => $organizationId]);
        }

        $agentUrl = AgentResource::getUrl('create');
        if (get_current_team()->hasAgent()) {
            $agentUrl = AgentResource::getUrl('index');
        }

        return [

            'default' => [
                'Dashboard' => [
                    'icon' => 'squares-2x2',
                    'route' => 'filament.app.pages.dashboard',
                ],
                'Organization' => [
                    'icon' => 'building-office',
                    'url' => $organizationUrl,
                    'visibleClosure' => fn () => Gatekeeper::userCan('update', Organization::class),
                ],
                ContributorResource::class,
                ListCollectionResource::class,
                'Agents' => [
                    'icon' => 'sparkles',
                    'url' => $agentUrl,
                    'visibleClosure' => fn () => Feature::active(Agents::class) && Gatekeeper::userCan('update', Agent::class),
                ],
            ],

            'Content' => [
                'Bulk Import' => [
                    'icon' => 'arrow-up-on-square-stack',
                    'url' => BulkImportContent::getUrl(),
                    'visibleClosure' => fn () =>
                        (
                            Feature::active(BulkImport::class) ||
                            get_current_team()->getPlanOption('bulk_import')
                        ) &&
                        Gatekeeper::userCan('create', Collection::class) &&
                        Gatekeeper::userCan('create', Source::class)
                    ,
                ],
                BookSourceResource::class,
                ArticleSourceResource::class,
                UrlSourceResource::class,
                EpisodeSourceResource::class,
                YoutubeSourceResource::class,
                MediaSourceResource::class,
            ],

            'Analytics' => [
                'visibleClosure' => fn () => Feature::active(Analytics::class) && get_current_team()->getPlanOption('analytics'),
                'items' => [
                    'Agents' => [
                        'icon' => 'sparkles',
                        'url' => AgentAnalytics::getUrl(),
                    ],
                    'Content' => [
                        'icon' => 'document',
                        'url' => SourceAnalytics::getUrl(),
                    ],
                    'Collections' => [
                        'icon' => 'document-duplicate',
                        'url' => CollectionAnalytics::getUrl(),
                    ],
                    'Contributors' => [
                        'icon' => 'academic-cap',
                        'url' => ContributorAnalytics::getUrl(),
                    ],
                    'Organization' => [
                        'icon' => 'building-office',
                        'url' => $organizationId ? OrganizationAnalytics::getUrl(['model_id'=>$organizationId]) : OrganizationAnalytics::getUrl(),
                    ],
                ],
            ],

        ];

    }
}
