<?php

namespace App\Filament\Resources\CollectionResource\Pages;

use App\Enums\CollectionType;
use App\Filament\Pages\App\Analytics\CollectionAnalytics;
use App\Filament\Resources\CollectionResource;
use App\Filament\Templates\EditRecord as BaseEditRecord;
use App\Filament\Traits\HasFields;
use App\Jobs\ImportSourceDataJob;
use App\Jobs\ReimportCollectionSourcesJob;
use App\Models\Model;
use App\Services\Gatekeeper;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;

class EditRecord extends BaseEditRecord
{
    use HasFields;

    protected static string $resource = CollectionResource::class;

    protected static ?string $importSourcesJob = null;

    /**
     * @return array|Action[]|ActionGroup[]
     */
    protected function getHeaderActions(): array
    {

        $actions = [

            Action::make('Re-Import Existing Sources')
                ->extraAttributes(['id' => btn_id('reimport-sources'), 'class' => 'gap-0 2xl:gap-1.5'])
                ->label('Re-Import Existing Sources')
                ->tooltip('Re-Import Existing Sources')
                ->icon('heroicon-s-arrow-path')
                ->outlined()
                ->labeledFrom('2xl')
                ->requiresConfirmation()
                ->action(function (array $data, $record) {
                    dispatch(new ReimportCollectionSourcesJob($record));
                    Notification::make()
                        ->title('Source re-import initiated')
                        ->success()
                        ->send()
                    ;
                    return true;
                })
                ->modalHeading('Re-Import Existing Sources')
                ->modalDescription('Sources will re-import starting from least recently imported.')
                ->modalIcon('heroicon-s-arrow-path')
                ->visible(Gatekeeper::userCan('import', static::$resource::getModel()) && ! is_null(static::$importSourcesJob))
            ,

            Action::make('Import New Sources')
                ->extraAttributes(['id' => btn_id('import-sources'), 'class' => 'gap-0 2xl:gap-1.5'])
                ->label('Import New Sources')
                ->tooltip('Import New Sources')
                ->icon('heroicon-s-cloud-arrow-down')
                ->labeledFrom('2xl')
                ->requiresConfirmation()
                ->form([
                    Toggle::make('full')
                        ->label('Force Full Source Import')
                        ->helperText('
                            Toggle this option to force a full import of all Sources.
                            Only the latest Sources will imported if this option is not toggled.
                        '),
                ])
                ->action(function (array $data, $record) {
                    Log::debug(static::$importSourcesJob);
                    dispatch(new static::$importSourcesJob($record, boolval($data['full'])));
                    Notification::make()
                        ->title('Source import initiated')
                        ->success()
                        ->send()
                    ;
                    return true;
                })
                ->modalHeading('Import New Sources')
                ->modalDescription('New sources will be imported.')
                ->modalIcon('heroicon-s-cloud-arrow-down')
                ->visible(Gatekeeper::userCan('import', static::$resource::getModel()) && ! is_null(static::$importSourcesJob))
            ,

            static::getCollectionReplicateHeaderAction(),

            static::getAnalyzeHeaderAction(CollectionAnalytics::class),

            static::decorateViewListingAction(Action::make('Preview'))
                ->extraAttributes(['class' => 'ml-0']),

            static::decorateViewReferenceAction(Action::make('Reference')),

        ];

        return array_merge(
            $this->getModerationHeaderActions(),
            $actions,
            $this->getLockActions(),
        );

    }

    protected static function getCollectionReplicateHeaderAction(): Action
    {
        return static::getReplicateHeaderAction(
            [
                static::getFormFieldName()
                    ->required(),
                static::getFormFieldLanguage(),
                static::getFormFieldCollectionUrl(),
                static::getFormFieldSourceUrlRestriction(static::$resource::getCollectionType()),
                Toggle::make('auto_import_sources')
                    ->label('Auto-Import New Sources')
                    ->helperText('New Sources associated with this Collection will automatically be imported every 24 hours.')
                    ->visible(fn (Get $get) => (in_array($get('type'), [CollectionType::RSS->value, CollectionType::WEBSITE->value, CollectionType::CHANNEL->value, CollectionType::PODCAST->value])))
                    ->disabled(fn (Get $get) => (empty($get('url')) && empty($get('external_id'))))
                    ->hintIcon(
                        fn (Get $get) => (empty($get('url')) && empty($get('external_id'))) ? 'heroicon-s-question-mark-circle' : null,
                        tooltip: fn (Get $get) => (empty($get('url')) && empty($get('external_id'))) ? 'Enter a URL to enable this option.' : null
                    )
                ,
            ],
            ['slug', 'is_locked']
        )
            ->extraAttributes(['id' => btn_id('duplicate'), 'class' => 'gap-0 2xl:gap-1.5'])
            ->label('Duplicate')
            ->tooltip('Duplicate')
            ->labeledFrom('2xl')
        ;
    }

}
