<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Enums\CollectionType;
use App\Features\AgentMedia;
use App\Filament\Resources\AgentResource;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFilters;
use App\Models\Agent;
use App\Models\Collection;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Filament\Forms;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Pages\EditRecord\Concerns\Translatable;
use Filament\Support\Enums\IconPosition;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Laravel\Pennant\Feature;
use RalphJSmit\Filament\RecordFinder\Forms\Components\RecordFinder;

class EditBranding extends EditTemplate
{
    use HasColumns;
    use HasFilters;

    protected static ?string $navigationLabel = 'Branding';

    public function form(Form $form): Form
    {
        $canWhiteLabel = get_current_team()->getPlanOption('agent.white_label');
        $upgradeNotice = AgentResource::getUpgradeNotice()->visible(!$canWhiteLabel);
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getLogoTab($canWhiteLabel, $upgradeNotice),
                        static::getColorsTab($canWhiteLabel, $upgradeNotice),
                        static::getBackgroundTab($canWhiteLabel, $upgradeNotice),
                        static::getFontsTab($canWhiteLabel, $upgradeNotice),
                        static::getStylesTab($canWhiteLabel, $upgradeNotice),
                    ])
                    ->persistTabInQueryString()
                    ->id('branding')
            ])
            ->disabled(AgentResource::getRestrictEditingClosure())
            ->columns(1)
        ;
    }

    protected static function getLogoTab(bool $canWhiteLabel, $upgradeNotice): Tabs\Tab
    {
        return Tabs\Tab::make('Logo')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        $upgradeNotice,
                        static::getFormFieldImage('agents/images', 'image_path', 'Header Logo'),
                        static::getFormFieldImage('agents/favicons', 'favicon_path', 'Favicon')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The icon that appears in the browser tab.',
                            )
                        ,
                    ])
                ,
            ])
            ->disabled(!$canWhiteLabel)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getColorsTab(bool $canWhiteLabel, $upgradeNotice): Tabs\Tab
    {
        return Tabs\Tab::make('Colors')
            ->schema([
                Grid::make(2)
                    ->relationship('frontend')
                    ->schema([
                        $upgradeNotice,
                        static::getFormFieldSelect('theme', ['dark' => 'Dark', 'light' => 'Light'], 'Theme')
                            ->default('dark')
                        ,
                        ColorPicker::make('primary_color')
                            ->label('Primary Color')
                            ->required()
                            ->default(env('AGENT_DEFAULT_PRIMARY_COLOR', '#3b82f6'))
                        ,
                    ])
                ,
            ])
            ->disabled(!$canWhiteLabel)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getBackgroundTab(bool $canWhiteLabel, $upgradeNotice): Tabs\Tab
    {
        return Tabs\Tab::make('Background')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        $upgradeNotice,
                        ColorPicker::make('background_color')
                            ->label('Background Color')
                        ,
                        Forms\Components\FileUpload::make('background_path')
                            ->label('Background Image / Video')
                            ->directory('agents/backgrounds')
                            ->maxSize(10240)
                            ->acceptedFileTypes([
                                'image/*',
                                'video/*',
                            ])
                            ->helperText('Up to 10 MB')
                        ,
                    ])
                ,
            ])
            ->disabled(!$canWhiteLabel)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getFontsTab(bool $canWhiteLabel, $upgradeNotice): Tabs\Tab
    {
        return Tabs\Tab::make('Fonts')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        $upgradeNotice,
                        static::getFormFieldUrl('display_font_url', 'Headline Font URL')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Any web-accessible font URL may be entered here — including a Google font.',
                            )
                            ->live()
                        ,
                        static::getFormFieldUrl('body_font_url', 'Body Font URL')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Any web-accessible font URL may be entered here — including a Google font.',
                            )
                            ->live()
                        ,
                        TextInput::make('display_font_name')
                            ->label('Display Font Name')
                            ->maxLength(100)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The name of the font. Google Font names will be automatically added.',
                            )
                        ,
                        TextInput::make('body_font_name')
                            ->label('Body Font Name')
                            ->maxLength(100)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The name of the font. Google Font names will be automatically added.',
                            )
                        ,
                    ])
                ,
            ])
            ->disabled(!$canWhiteLabel)
            ->columns(static::getColumnBreakpoints())
        ;
    }

    protected static function getStylesTab(bool $canWhiteLabel, $upgradeNotice): Tabs\Tab
    {
        return Tabs\Tab::make('Custom Styles')
            ->schema([
                Grid::make(static::getColumnBreakpoints())
                    ->relationship('frontend')
                    ->schema([
                        $upgradeNotice,
                        Textarea::make('custom_styles')
                            ->label('Custom Styles')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: "You may add custom CSS here, and it will be applied your Agent's interface."
                            )
                            ->helperText(view('components.help.agent-custom-styles'))
                            ->maxLength(1000)
                            ->rows(10)
                            ->extraAttributes(['class'=>'font-mono'])
                            ->columnSpan(2)
                        ,
                    ])
                ,
            ])
            ->disabled(!$canWhiteLabel)
            ->columns(static::getColumnBreakpoints())
        ;
    }

}
